import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/features/app_navigation/widgets/ia_bottom_navigation_bar.dart';
import 'package:ivent_app/routes/app_pages.dart';
import 'package:ivent_app/shared/views/root_page.dart';

class AppNavigation extends GetView<AppNavigationController> {
  const AppNavigation({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const Drawer(child: Text('Drawer')),
      body: Obx(() {
        final currentIndex = controller.currentIndex;
        return IndexedStack(
          index: currentIndex,
          children: controller.tabs
              .asMap()
              .entries
              .map((entry) => _LazyNavigator(
                    index: entry.key,
                    initialRoute: entry.value.route,
                    arguments: entry.value.arguments,
                    isActive: entry.key == currentIndex,
                  ))
              .toList(),
        );
      }),
      bottomNavigationBar:
          const IaBottomNavigationBar(iconPaths: [AppAssets.house01, AppAssets.bell, AppAssets.user01]),
    );
  }
}

class _LazyNavigator extends StatefulWidget {
  final int index;
  final String initialRoute;
  final dynamic arguments;
  final bool isActive;

  const _LazyNavigator({
    required this.index,
    required this.initialRoute,
    this.arguments,
    required this.isActive,
  });

  @override
  State<_LazyNavigator> createState() => _LazyNavigatorState();
}

class _LazyNavigatorState extends State<_LazyNavigator> {
  bool isBuilt = false;
  Widget cachedNavigator = const SizedBox.shrink();

  @override
  Widget build(BuildContext context) {
    if (!isBuilt && widget.isActive) {
      debugPrint('Building navigator for index: ${widget.index}');
      isBuilt = true;
      cachedNavigator = _Navigator(
        index: widget.index,
        initialRoute: widget.initialRoute,
        arguments: widget.arguments,
      );
    }
    return cachedNavigator;
  }
}

class _Navigator extends StatelessWidget {
  final int index;
  final String initialRoute;
  final dynamic arguments;

  const _Navigator({
    required this.index,
    required this.initialRoute,
    this.arguments,
  });

  @override
  Widget build(BuildContext context) {
    return Navigator(
      key: Get.nestedKey(index),
      initialRoute: initialRoute,
      onGenerateInitialRoutes: _generateInitialRoutes,
      onGenerateRoute: _generateRoute,
    );
  }

  GetPage _getPage(String? routeName) {
    return AppPages.routes.firstWhere(
      (p) => p.name == routeName,
      orElse: () => AppPages.notFoundPage,
    );
  }

  List<Route> _generateInitialRoutes(NavigatorState navigator, String initialRouteName) {
    return [
      MaterialPageRoute(
        builder: (_) => RootPage(
          routeName: initialRouteName,
          index: index,
          arguments: arguments,
        ),
      ),
    ];
  }

  Route _generateRoute(RouteSettings settings) {
    final page = _getPage(settings.name);
    return GetPageRoute(
      settings: settings,
      page: page.page,
      binding: page.binding,
    );
  }
}
