import 'package:get/get.dart';

class NavigationData {
  final String route;
  final dynamic arguments;
  final RxBool _isDrawerVisible;
  final RxBool _isNavBarVisible;
  late final bool _defaultDrawerVisibility;
  late final bool _defaultNavBarVisibility;

  NavigationData(
    this.route, {
    this.arguments,
    bool isDrawerVisible = true,
    bool isNavBarVisible = true,
  })  : _isDrawerVisible = isDrawerVisible.obs,
        _isNavBarVisible = isNavBarVisible.obs {
    _defaultDrawerVisibility = isDrawerVisible;
    _defaultNavBarVisibility = isNavBarVisible;
  }

  bool get isDrawerVisible => _isDrawerVisible.value;
  bool get isNavBarVisible => _isNavBarVisible.value;

  set isDrawerVisible(bool value) => _isDrawerVisible.value = value;
  set isNavBarVisible(bool value) => _isNavBarVisible.value = value;

  void resetVisibility() {
    isDrawerVisible = _defaultDrawerVisibility;
    isNavBarVisible = _defaultNavBarVisibility;
  }
}
