import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/app_navigation/entities/navigation_data.dart';
import 'package:ivent_app/features/home/<USER>';
import 'package:ivent_app/features/profile/profile_pages.dart';
import 'package:ivent_app/routes/notifications.dart';

class AppNavigationController extends GetxController {
  final _currentIndex = 0.obs;

  int get currentIndex => _currentIndex.value;
  bool get isDrawerVisible => tabs[currentIndex].isDrawerVisible;
  bool get isNavBarVisible => tabs[currentIndex].isNavBarVisible;

  final authService = Get.find<AuthService>();
  String? get sessionId => authService.sessionUser?.sessionId;

  List<NavigationData> get tabs => [
        NavigationData(HomePages.homePage),
        NavigationData(NotificationsPages.notifications),
        NavigationData(ProfilePages.userProfile, arguments: sessionId!),
      ];

  void changeTab(int index) {
    if (index == currentIndex) {
      final data = tabs[index];
      data.resetVisibility();
      Get.offAllNamed(data.route, id: index, arguments: data.arguments);
      setDrawerVisibility(index, data.isDrawerVisible);
      setNavBarVisibility(index, data.isNavBarVisible);
    }
    _currentIndex.value = index;
  }

  void setDrawerVisibility(int index, bool isVisible) => tabs[currentIndex].isDrawerVisible = isVisible;
  void setNavBarVisibility(int index, bool isVisible) => tabs[currentIndex].isNavBarVisible = isVisible;

  void showDrawer() => setDrawerVisibility(currentIndex, true);
  void hideDrawer() => setDrawerVisibility(currentIndex, false);
  void showNavBar() => setNavBarVisibility(currentIndex, true);
  void hideNavBar() => setNavBarVisibility(currentIndex, false);
}
