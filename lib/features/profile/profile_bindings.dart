import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_created_ivents_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_favorites_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_followers_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_followings_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_friends_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_joined_ivents_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_memories_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';
import 'package:ivent_app/features/profile/controllers/profile_social_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_user_info_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_vibes_controller.dart';

class ProfileBindings implements Bindings {
  ProfileBindings();

  @override
  void dependencies() {
    final service = Get.find<AuthService>();
    final userId = Get.arguments;
    print(' [PROFILE] userId: $userId');
    final isOwnProfile = userId == service.sessionUser!.sessionId;
    final state = Get.put(ProfileSharedState(userId), tag: userId, permanent: isOwnProfile);
    final isCreator = service.sessionUser!.sessionRole == UserRoleEnum.creator;

    if (isCreator) Get.lazyPut(() => ProfileCreatedIventsController(service, state), tag: userId, fenix: true);
    Get.lazyPut(() => ProfileJoinedIventsController(service, state), tag: userId, fenix: true);
    Get.lazyPut(() => ProfileFollowersController(service, state), tag: userId, fenix: true);
    Get.lazyPut(() => ProfileFriendsController(service, state), tag: userId, fenix: true);
    Get.lazyPut(() => ProfileUserInfoController(service, state), tag: userId, fenix: true);
    Get.lazyPut(() => ProfileSocialController(service, state), tag: userId, fenix: true);
    Get.lazyPut(() => ProfileVibesController(service, state), tag: userId, fenix: true);

    if (!isOwnProfile) return;

    // First person view information
    Get.lazyPut(() => ProfileMemoriesController(service, state), tag: userId, fenix: true);
    Get.lazyPut(() => ProfileFollowingsController(service, state), tag: userId, fenix: true);
    Get.lazyPut(() => ProfileFavoritesController(service, state), tag: userId, fenix: true);
  }
}
