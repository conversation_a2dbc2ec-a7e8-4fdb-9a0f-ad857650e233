import 'package:get/get.dart';
import 'package:ivent_app/features/profile/profile_bindings.dart';
import 'package:ivent_app/features/profile/views/creator_ivents.dart';
import 'package:ivent_app/features/profile/views/user_favorites.dart';
import 'package:ivent_app/features/profile/views/user_followers.dart';
import 'package:ivent_app/features/profile/views/user_followings.dart';
import 'package:ivent_app/features/profile/views/user_friends.dart';
import 'package:ivent_app/features/profile/views/user_ivents.dart';
import 'package:ivent_app/features/profile/views/user_profile.dart';

class ProfilePages {
  ProfilePages._();

  static const _prefix = '/userProfile';

  static const userProfile = '$_prefix';
  static const creatorIvents = '$_prefix/creatorIvents';
  static const userIvents = '$_prefix/userIvents';
  static const userFollowers = '$_prefix/userFollowers';
  static const userFriends = '$_prefix/userFriends';
  static const userFollowings = '$_prefix/userFollowings';
  static const userFavorites = '$_prefix/userFavorites';

  static final routes = [
    GetPage(
      name: userProfile,
      page: () => UserProfile(userId: Get.arguments),
      binding: ProfileBindings(userId: Get.arguments),
    ),
    GetPage(name: creatorIvents, page: () => CreatorIvents(userId: Get.arguments)),
    GetPage(name: userIvents, page: () => UserIvents(userId: Get.arguments)),
    GetPage(name: userFollowers, page: () => UserFollowers(userId: Get.arguments)),
    GetPage(name: userFriends, page: () => UserFriends(userId: Get.arguments)),
    GetPage(name: userFollowings, page: () => UserFollowings(userId: Get.arguments)),
    GetPage(name: userFavorites, page: () => UserFavorites(userId: Get.arguments)),
  ];
}
