import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/routes/app_pages.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:package_info_plus/package_info_plus.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize date formatting for Turkish locale
  await initializeDateFormatting('tr_TR', null);
  Intl.defaultLocale = 'tr_TR';

  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  await dotenv.load(fileName: '.env');

  debugPrint('🚀 [Main] Starting app on Platform: ${Platform.operatingSystem}');

  // Set Mapbox access token globally
  final mapboxToken = dotenv.env['MAPBOX_ACCESS_TOKEN'];
  if (mapboxToken != null && mapboxToken.isNotEmpty) {
    MapboxOptions.setAccessToken(mapboxToken);
  } else {
    debugPrint('❌ [Mapbox] Token not found in .env file');
  }

  // Initialize AuthService
  await Get.putAsync(() => AuthService().init());

  runApp(IventApp());
}

class IventApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(colorScheme: AppColors.lightColorScheme),
      darkTheme: ThemeData(colorScheme: AppColors.darkColorScheme),
      themeMode: ThemeMode.light,
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('tr', 'TR'),
        Locale('en', 'US'),
      ],
      locale: const Locale('tr', 'TR'),
      initialRoute: AppPages.initial,
      getPages: AppPages.routes,
      unknownRoute: AppPages.notFoundPage,
      builder: (context, child) => RootShell(child: child ?? const SizedBox.shrink()),
    );
  }
}

class RootShell extends StatelessWidget {
  final Widget child;

  const RootShell({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) return;
        Get.back();
      },
      child: Stack(
        children: [
          child,
          const Positioned(right: 2, bottom: 2, child: _VersionBanner()),
        ],
      ),
    );
  }
}

class _VersionBanner extends StatelessWidget {
  const _VersionBanner();

  @override
  Widget build(BuildContext context) {
    return Material(
      child: FutureBuilder<PackageInfo>(
        future: PackageInfo.fromPlatform(),
        builder: (context, snapshot) {
          if (!snapshot.hasData) return const SizedBox.shrink();
          final version = '${snapshot.data!.version}+${snapshot.data!.buildNumber}, ${kDebugMode ? 'debug' : 'test'}';
          return IaRoundedContainer(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            roundness: 4,
            color: AppColors.semiTransparentBlackZero,
            child: Text(version, style: AppTextStyles.size16MediumWhite.copyWith(fontSize: 8)),
          );
        },
      ),
    );
  }
}
