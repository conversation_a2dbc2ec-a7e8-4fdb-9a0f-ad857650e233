import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';

class RootPage extends StatefulWidget {
  final String routeName;
  final int index;
  final dynamic arguments;

  const RootPage({
    super.key,
    required this.routeName,
    required this.index,
    this.arguments,
  });

  @override
  State<RootPage> createState() => _RootPageState();
}

class _RootPageState extends State<RootPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) => _navigateToNextScreen());
  }

  Future<void> _navigateToNextScreen() async {
    // Use Get.offNamed to replace the current route instead of pushing a new one
    // This ensures the arguments are properly set in the navigation context
    Get.offNamed(widget.routeName, arguments: widget.arguments, id: widget.index);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Center(
          child: Text(
        '${widget.routeName},\narguments: ${widget.arguments},\nid: ${widget.index}',
      )),
    );
  }
}
